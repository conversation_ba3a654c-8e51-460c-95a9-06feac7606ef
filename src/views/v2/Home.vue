<template>
  <div class="v2-home">
    <!-- 背景 -->
    <div class="background-container">
      <!-- 底图 -->
      <div class="bg-base">
        <img
          src="@/assets/img/homev2/大bg.png"
          alt="底图"
          class="bg-base-image"
        />
      </div>
      <!-- 上半部分覆盖图 -->
      <div class="bg-top">
        <img
          src="@/assets/img/homev2/bg.png"
          alt="上半部分"
          class="bg-top-image"
        />
      </div>
    </div>

    <!-- 页面头部 -->
    <div class="header">
      <div class="header-left">
        <div class="logo-section">
          <span class="logo-text">兴山农村智慧供水平台</span>
        </div>
      </div>
    </div>

    <!-- 供水监管平台 -->
    <div class="platform-section">
      <div class="platform-icon" @click="handlePlatformClick">
        <img src="@/assets/img/homev2/平台.png" alt="供水监管平台" />
        <div class="platform-title">供水监管平台</div>
      </div>
    </div>

    <!-- 数据统计区域 -->
    <div class="stats-section">
      <div class="stat-item">
        <img src="@/assets/img/homev2/供水总量.png" alt="供水总量" />
        <div class="stat-content">
          <div class="stat-number">125,890</div>
          <div class="stat-label">供水总量(m³)</div>
        </div>
      </div>
      <div class="stat-item">
        <img src="@/assets/img/homev2/管网长度.png" alt="管网长度" />
        <div class="stat-content">
          <div class="stat-number">1256</div>
          <div class="stat-label">管网长度(km)</div>
        </div>
      </div>
      <div class="stat-item">
        <img src="@/assets/img/homev2/人口.png" alt="服务人口" />
        <div class="stat-content">
          <div class="stat-number">89,657</div>
          <div class="stat-label">服务人口(人)</div>
        </div>
      </div>
      <div class="stat-item">
        <img src="@/assets/img/homev2/达标率.png" alt="达标率" />
        <div class="stat-content">
          <div class="stat-number">99.8</div>
          <div class="stat-label">达标率(%)</div>
        </div>
      </div>
    </div>

    <!-- 业务模块区域 -->
    <div class="business-section">
      <div class="business-row">
        <div class="business-card" @click="handleCardClick(businessCards[0])">
          <div class="card-bg supply-dispatch"></div>
          <div class="card-content">
            <div class="card-icon">
              <img src="@/assets/img/homev2/供水调度.png" alt="供水调度" />
            </div>
            <div class="card-title">供水调度</div>
          </div>
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[1])">
          <div class="card-bg pipe-leakage"></div>
          <div class="card-content">
            <div class="card-icon">
              <img src="@/assets/img/homev2/管网漏损.png" alt="管网漏损" />
            </div>
            <div class="card-title">管网漏损</div>
          </div>
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[2])">
          <div class="card-bg business-charge"></div>
          <div class="card-content">
            <div class="card-icon">
              <img src="@/assets/img/homev2/营收收费.png" alt="营业收费" />
            </div>
            <div class="card-title">营业收费</div>
          </div>
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[3])">
          <div class="card-bg pipe-gis"></div>
          <div class="card-content">
            <div class="card-icon">
              <img src="@/assets/img/homev2/管网GIS.png" alt="管网GIS" />
            </div>
            <div class="card-title">管网GIS</div>
          </div>
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[4])">
          <div class="card-bg water-quality"></div>
          <div class="card-content">
            <div class="card-icon">
              <img src="@/assets/img/homev2/水质监测.png" alt="水质检测" />
            </div>
            <div class="card-title">水质检测</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 平台管理区域 -->
    <div class="platform-management">
      <div class="platform-row">
        <div class="platform-card">
          <img src="@/assets/img/homev2/公共管理平台.png" alt="公共管理平台" />
          <div class="platform-name">公共管理平台</div>
        </div>
        <div class="platform-card">
          <img
            src="@/assets/img/homev2/物联网接入平台.png"
            alt="物联网接入平台"
          />
          <div class="platform-name">物联网接入平台</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/GIS.png" alt="GIS服务平台" />
          <div class="platform-name">GIS服务平台</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/工单.png" alt="工单接入平台" />
          <div class="platform-name">工单接入平台</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/统一报警.png" alt="统一报警中心" />
          <div class="platform-name">统一报警中心</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/视频监控.png" alt="视频监控平台" />
          <div class="platform-name">视频监控平台</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "V2Home",
  data() {
    return {
      // 业务模块卡片数据
      businessCards: [
        {
          id: 1,
          title: "供水调度",
          route: { name: "WsScreen" },
        },
        {
          id: 2,
          title: "管网漏损",
          route: { name: "WlScreen" },
        },
        {
          id: 3,
          title: "营业收费",
          route: null, // 暂时不做跳转
        },
        {
          id: 4,
          title: "管网GIS",
          route: { name: "PipeNetworkIndex" },
        },
        {
          id: 5,
          title: "水质检测",
          route: { name: "WqScreen" },
        },
      ],
    };
  },
  methods: {
    // 处理业务卡片点击
    handleCardClick(card) {
      if (card && card.route) {
        this.$router.push(card.route);
      }
    },
    // 处理供水监管平台点击 - 跳转到供水一张图
    handlePlatformClick() {
      this.$router.push({ name: "InfoMap" });
    },
    // 返回按钮
    goBack() {
      // 可以根据需要实现返回逻辑
      console.log("返回白沙下app");
    },
  },
};
</script>

<style lang="scss" scoped>
.v2-home {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  color: #fff;

  // 背景容器 - 绝对定位铺满全屏
  .background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    // 底图 - 铺满整个页面
    .bg-base {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      .bg-base-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    // 上半部分覆盖图
    .bg-top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 520px;
      z-index: 2;

      .bg-top-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  // 页面头部 - 绝对定位
  .header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;

    .header-left {
      .logo-section {
        position: absolute;
        left: 50%;
        top: 34px;
        transform: translateX(-50%);

        .logo-text {
          width: 379px;
          height: 46px;
          font-family: PangMenZhengDao, PangMenZhengDao;
          font-weight: 400;
          font-size: 40px;
          color: #ffffff;
          line-height: 46px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          opacity: 1;
        }
      }
    }

    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        gap: 30px;

        .back-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          color: #fff;
          font-size: 14px;

          img {
            width: 16px;
            height: 16px;
          }

          &:hover {
            opacity: 0.8;
          }
        }

        .user-section {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #fff;
          font-size: 14px;

          img {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  // 供水监管平台 - 根据设计稿精确定位
  .platform-section {
    position: absolute;
    top: 110px;
    left: 880px;
    z-index: 5;

    .platform-icon {
      cursor: pointer;
      transition: transform 0.3s ease;
      text-align: center;
      opacity: 1; // 100% 不透明度

      &:hover {
        transform: scale(1.05);
      }

      img {
        width: 160px;
        height: 242px;
        display: block;
      }

      .platform-title {
        width: 168px;
        height: 37px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 700;
        font-size: 28px;
        color: #ffffff;
        line-height: 37px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin: 10px auto 0 auto; // 上边距10px，左右居中
      }
    }
  }

  // 数据统计区域 - 根据设计稿精确定位
  .stats-section {
    position: absolute;
    top: 420px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    width: 1620px;
    height: 100px;
    background-image: url("~@/assets/img/homev2/透明底.png");
    background-size: 1620px 100px;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    justify-content: space-between;

    .stat-item {
      position: relative;
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;

      // 添加分割线（除了最后一个卡片）
      &:not(:last-child)::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: auto;
        height: auto;
        background-image: url("~@/assets/img/homev2/分割线.png");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        // 根据实际分割线图片尺寸调整
        min-width: 2px;
        min-height: 60px;
      }

      img {
        position: absolute;
        left: 80px;
        top: 50%;
        transform: translateY(-50%);
        // 保持切图原比例，不定死尺寸
        max-width: none;
        max-height: none;
      }

      .stat-content {
        position: absolute;
        left: 80px;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 70px; // 切图宽度 + 10px间距

        .stat-number {
          // 移除固定宽度，让数字长度自适应
          min-width: 58px;
          height: 32px;
          font-family: DIN, DIN;
          font-weight: 700;
          font-size: 30px;
          color: #ffffff;
          line-height: 32px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-bottom: 8px;
          white-space: nowrap; // 防止换行
        }

        .stat-label {
          // 移除固定宽度，让文字长度自适应
          min-width: 91px;
          height: 21px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          white-space: nowrap; // 防止换行
        }
      }

      // 针对管网长度卡片的特殊处理（如果切图比较大）
      &:nth-child(2) .stat-content {
        margin-left: 85px; // 为管网长度增加更多间距
      }
    }
  }

  // 业务模块区域 - 绝对定位
  .business-section {
    position: absolute;
    top: 550px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    width: 1000px;

    .business-row {
      display: flex;
      justify-content: center;
      gap: 20px;

      .business-card {
        position: relative;
        width: 180px;
        height: 120px;
        cursor: pointer;
        transition: transform 0.3s ease;
        border-radius: 12px;
        overflow: hidden;

        &:hover {
          transform: translateY(-5px);
        }

        .card-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;

          // 不同业务模块的背景色
          &.supply-dispatch {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
          }

          &.pipe-leakage {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.business-charge {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.pipe-gis {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.water-quality {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          }
        }

        .card-content {
          position: relative;
          z-index: 2;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 20px;

          .card-icon {
            margin-bottom: 10px;

            img {
              width: 32px;
              height: 32px;
            }
          }

          .card-title {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }

  // 平台管理区域 - 绝对定位在页面底部
  .platform-management {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    width: 1200px;

    .platform-row {
      display: flex;
      justify-content: center;
      gap: 30px;

      .platform-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }

        img {
          width: 80px;
          height: 80px;
        }

        .platform-name {
          font-size: 12px;
          color: #fff;
          text-align: center;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>
